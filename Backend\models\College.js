const mongoose = require('mongoose');

const collegeSchema = new mongoose.Schema({
    collegeId: {
        type: String,
        required: true,
        unique: true
    },
    collegeName: {
        type: String,
        required: true
    },
    adminId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    departments: [{
        type: String
    }],
    students: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }],
    downloads: [{
        type: String
    }],
    createdAt: {
        type: Date,
        default: Date.now
    }
});

module.exports = mongoose.model('College', collegeSchema);
