const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    name: {
        first: { type: String, required: true },
        last: { type: String, required: true }
    },
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true
    },
    password: {
        type: String,
        required: true
    },
    role: {
        type: String,
        enum: ['student', 'trainer', 'college', 'admin'],
        default: 'student',
        required: true
    },
    mobile: String,
    whatsapp: String,
    dob: Date,

    // Email Verification via OTP
    otp: {
        code: String,
        expiresAt: Date,
        verified: Boolean,
        attempts: { type: Number, default: 0 },
        lastSent: Date,
        resendCount: { type: Number, default: 0 },
        resendResetTime: Date
    },

    // Timestamps
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },

    // Common for All (Student, Trainer, College)
    collegeId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'College'
    },

    // ======== STUDENT-SPECIFIC FIELDS ========
    isProfessional: {
        type: Boolean,
        default: false
    },
    collegeName: String,
    passingYear: Number,
    department: String,
    preferredLocation: {
        type: String,
        enum: ['Vijaynagar', 'Gulbarga', 'Belgavi', 'Other']
    },
    semester: Number,
    batchId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Batch'
    },
    certificates: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Certificate'
    }],
    status: {
        type: String,
        enum: ['active', 'completed', 'dropped'],
        default: 'active'
    },

    // ======== TRAINER-SPECIFIC FIELDS ========
    trainerId: String,
    qualification: String,
    experience: String,
    otherDetails: String,
    assignedBatches: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Batch'
    }]
}, {
    timestamps: true
});

module.exports = mongoose.model('User', userSchema);
