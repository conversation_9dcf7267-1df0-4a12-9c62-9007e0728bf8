const mongoose = require('mongoose');

const assignmentSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true
    },

    description: {
        type: String
    },

    batchId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Batch',
        required: true
    },

    courseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true
    },

    fileUrl: {
        type: String, // Firebase/S3 link for the uploaded file
        required: true
    },

    dueDate: {
        type: Date,
        required: true
    },

    maxMarks: {
        type: Number,
        default: 100
    },

    instructions: {
        type: String
    },

    uploadedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // Trainer who uploaded
        required: true
    },

    createdAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});

module.exports = mongoose.model('Assignment', assignmentSchema);
