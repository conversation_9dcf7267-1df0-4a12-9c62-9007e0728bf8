const mongoose = require('mongoose');

const assessmentSchema = new mongoose.Schema({
    studentId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    batchId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Batch',
        required: true
    },
    courseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true
    },
    title: {
        type: String,
        required: true
    },
    type: {
        type: String,
        enum: ['quiz', 'assignment', 'exam', 'project'],
        required: true
    },
    marks: {
        type: Number,
        required: true
    },
    maxMarks: {
        type: Number,
        required: true
    },
    weightage: {
        type: Number, // percentage contribution to final grade
        default: 0
    },
    isFinal: {
        type: Boolean,
        default: false
    },
    date: {
        type: Date,
        default: Date.now
    },
    remarks: {
        type: String
    },
    uploadedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User' // trainer
    }
}, {
    timestamps: true
});

module.exports = mongoose.model('Assessment', assessmentSchema);
