const express = require('express');
const router = express.Router();
const trainerController = require('../controllers/trainerController');
const { protect, authorize } = require('../middlewares/authMiddleware');
const { validateCreateMaterial, validateCreateAssignment, validateAttendance, validateAssessment } = require('../middlewares/validationMiddleware');
const upload = require('../middlewares/uploadMiddleware');

// Apply authentication and trainer role check to all routes
router.use(protect);              // middleware to check JWT
router.use(authorize('trainer'));
// ================================
// TRAINER DASHBOARD
// ================================
router.get('/dashboard', trainerController.getDashboard);

// ================================
// BATCH MANAGEMENT
// ================================
router.get('/batches', trainerController.getAssignedBatches);
router.get('/batches/:batchId', trainerController.getBatchDetails);
router.get('/batches/:batchId/students', trainerController.getBatchStudents);

// // COURSE MATERIALS
router.get('/batches/:batchId/materials', trainerController.getBatchMaterials);
router.post(
    '/batches/:batchId/materials',
    upload.single('file'), // 👈 includes all 3: upload, error handling, validation
    trainerController.uploadCourseMaterial
);
router.get('/materials/:materialId', trainerController.getMaterialDetails);
router.put('/materials/:materialId', trainerController.updateMaterial);
router.delete('/materials/:materialId', trainerController.deleteMaterial);

// // ================================
// // ASSIGNMENTS
// // ================================
// router.get('/batches/:batchId/assignments', trainerController.getBatchAssignments);
// router.post('/batches/:batchId/assignments',
//     upload.single('file'),
//     validateCreateAssignment,
//     trainerController.createAssignment
// );
// router.get('/assignments/:assignmentId', trainerController.getAssignmentDetails);
// router.put('/assignments/:assignmentId', trainerController.updateAssignment);
// router.delete('/assignments/:assignmentId', trainerController.deleteAssignment);

// // Assignment submissions
// router.get('/assignments/:assignmentId/submissions', trainerController.getAssignmentSubmissions);
// router.put('/assignments/:assignmentId/submissions/:submissionId/grade', trainerController.gradeSubmission);

// // ================================
// // ATTENDANCE MANAGEMENT
// // ================================
// router.get('/batches/:batchId/attendance', trainerController.getBatchAttendance);
// router.post('/batches/:batchId/attendance',
//     validateAttendance,
//     trainerController.markAttendance
// );
// router.put('/attendance/:attendanceId', trainerController.updateAttendance);
// router.get('/batches/:batchId/attendance/summary', trainerController.getAttendanceSummary);

// // ================================
// // ASSESSMENTS & MARKS
// // ================================
// router.get('/batches/:batchId/assessments', trainerController.getBatchAssessments);
// router.post('/batches/:batchId/assessments',
//     validateAssessment,
//     trainerController.createAssessment
// );
// router.get('/assessments/:assessmentId', trainerController.getAssessmentDetails);
// router.put('/assessments/:assessmentId', trainerController.updateAssessment);
// router.delete('/assessments/:assessmentId', trainerController.deleteAssessment);

// // Upload assessment marks
// router.post('/assessments/:assessmentId/marks', trainerController.uploadAssessmentMarks);

// // ================================
// // NOTIFICATIONS
// // ================================
// router.get('/notifications', trainerController.getNotifications);
// router.post('/notifications', trainerController.sendNotification);
// router.put('/notifications/:notificationId/read', trainerController.markNotificationAsRead);

// // ================================
// // FEEDBACK FROM STUDENTS
// // ================================
// router.get('/feedback', trainerController.getStudentFeedback);
// router.get('/feedback/:feedbackId', trainerController.getFeedbackDetails);

// // ================================
// // REPORTS
// // ================================
// router.get('/reports/batch/:batchId', trainerController.getBatchReport);
// router.get('/reports/student/:studentId', trainerController.getStudentReport);
// router.get('/reports/attendance/:batchId', trainerController.getAttendanceReport);
// router.get('/reports/performance/:batchId', trainerController.getPerformanceReport);

// // ================================
// // PROFILE MANAGEMENT
// // ================================
// router.get('/profile', trainerController.getProfile);
// router.put('/profile', trainerController.updateProfile);

module.exports = router;