const mongoose = require('mongoose');

const courseMaterialSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    type: {
        type: String,
        enum: ['document', 'presentation', 'video', 'audio', 'image', 'other'],
        default: 'document'
    },
    fileUrl: {
        type: String,
        required: true
    },
    fileName: {
        type: String,
        required: true
    },
    fileSize: {
        type: Number // in bytes
    },
    batch: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Batch',
        required: true
    },
    uploadedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    downloadCount: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true
});

// Index for efficient queries
courseMaterialSchema.index({ batch: 1, uploadedBy: 1 });
courseMaterialSchema.index({ title: 'text', description: 'text' });

module.exports = mongoose.model('CourseMaterial', courseMaterialSchema);
