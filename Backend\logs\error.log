{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:01"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:15)\n    at startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:15)\n    at Object.<anonymous> (D:\\techvritti\\Collegemanagement\\Backend\\server.js:71:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)","timestamp":"2025-07-14 14:07:01"}
{"level":"error","message":"❌ Database connection attempt 2 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:02"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:02"}
{"level":"error","message":"❌ Database connection attempt 3 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:04"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:04"}
{"level":"error","message":"❌ Database connection attempt 4 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:08"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:08"}
{"level":"error","message":"❌ Database connection attempt 5 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:16"}
{"level":"error","message":"sendOtpEmail is not a function - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 16:55:39"}
{"level":"error","message":"User already exists with this email - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 16:56:18"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 17:11:30"}
{"level":"error","message":"Invalid OTP - POST /api/auth/verify-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 17:58:53"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 17:59:01"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 18:07:13"}
{"level":"error","message":"User already exists with this email - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 18:08:28"}
{"level":"error","message":"Invalid OTP - POST /api/auth/verify-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:10:02"}
{"level":"error","message":"Cannot destructure property 'email' of 'req.body' as it is undefined. - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:13:41"}
{"level":"error","message":"User not found - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:14:09"}
{"level":"error","message":"Wait 1 minute before requesting a new OTP - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:18:40"}
{"level":"error","message":"OTP resend limit reached for today. Try again later. - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:28:09"}
{"level":"error","message":"User already exists with this email - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 18:46:41"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 18:47:26"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 18:47:42"}
{"level":"error","message":"Wait 1 minute before requesting a new OTP - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:48:55"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 11:36:41"}
{"level":"error","message":"User validation failed: name.last: Path `name.last` is required., name.first: Path `name.first` is required., preferredLocation: `Pune` is not a valid enum value for path `preferredLocation`. - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 11:38:17"}
{"level":"error","message":"User validation failed: name.last: Path `name.last` is required., name.first: Path `name.first` is required. - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 11:39:21"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 11:40:11"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 12:01:00"}
{"level":"error","message":"OTP not found or user not registered - POST /api/auth/verify-otp - ::1","service":"college-management-system","timestamp":"2025-07-15 12:13:40"}
{"level":"error","message":"Forbidden - You do not have permission to access this resource - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 12:15:38"}
{"level":"error","message":"generateOtp is not defined - POST /api/admin/create-trainer - ::1","service":"college-management-system","timestamp":"2025-07-15 12:28:26"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-trainer - ::1","service":"college-management-system","timestamp":"2025-07-15 12:32:58"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:27:40"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:27:52"}
{"level":"error","message":"User validation failed: name.last: Path `name.last` is required., name.first: Path `name.first` is required. - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:28:08"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:29:42"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:32:49"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:32:53"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:33:52"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:34:29"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:34:37"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:34:49"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:35:03"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:39:15"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:50:44"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:51:57"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:04:12"}
{"level":"error","message":"E11000 duplicate key error collection: test.colleges index: collegeId_1 dup key: { collegeId: \"COL0001\" } - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:09:27"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:12:54"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:14:57"}
{"level":"error","message":"Cast to string failed for value \"{ '$regex': '^COL' }\" (type Object) at path \"collegeId\" for model \"College\" - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:21:05"}
{"level":"error","message":"Cast to string failed for value \"{ '$regex': '^COL', '$options': 'i' }\" (type Object) at path \"collegeId\" for model \"College\" - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:23:37"}
{"level":"error","message":"Cast to string failed for value \"{ '$regex': '^COL' }\" (type Object) at path \"collegeId\" for model \"College\" - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:40:48"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:44:57"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-15 17:47:52"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:53:05"}
{"level":"error","message":"Course not found - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:26:32"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:26:42"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:28:00"}
{"level":"error","message":"Course not found - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:29:19"}
{"level":"error","message":"Batch validation failed: department: Path `department` is required., status: `active` is not a valid enum value for path `status`. - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:31:03"}
{"level":"error","message":"Batch validation failed: status: `active` is not a valid enum value for path `status`. - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:35:38"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:36:58"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 10:26:48"}
{"level":"error","message":"Forbidden - You do not have permission to access this resource - GET /api/admin/report/user/6875fcfaa9639a2d69b063af - ::1","service":"college-management-system","timestamp":"2025-08-04 10:32:25"}
{"level":"error","message":"Cannot read properties of undefined (reading 'includes') - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 10:47:14"}
{"level":"error","message":"Cannot read properties of undefined (reading 'includes') - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 10:49:03"}
{"level":"error","message":"Batch validation failed: batchName: Path `batchName` is required. - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 10:53:41"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 10:56:44"}
{"level":"error","message":"Cannot populate path `trainers` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 11:00:15"}
{"level":"error","message":"Trainer is already assigned to this batch - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 11:00:58"}
{"level":"error","message":"Cannot populate path `users` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 11:01:13"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/report/batch/BAT0003 - ::1","service":"college-management-system","timestamp":"2025-08-04 11:07:57"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-08-04 11:19:06"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:34:05"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 12:35:36"}
{"level":"error","message":"Cannot populate path `college` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 12:38:06"}
{"level":"error","message":"Forbidden - You do not have permission to access this resource - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 12:40:46"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 12:43:20"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:49:53"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:53:14"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:53:21"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches/687652713d110f073072eed1 - ::1","service":"college-management-system","timestamp":"2025-08-04 12:55:20"}
{"level":"error","message":"Batch not found or not assigned to you - GET /api/trainers/batches/687652713d110f073072eed1 - ::1","service":"college-management-system","timestamp":"2025-08-04 12:56:25"}
{"level":"error","message":"Cloudinary upload error:","service":"college-management-system","timestamp":"2025-08-04 13:05:50"}
{"level":"error","message":"File upload failed - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials - ::1","service":"college-management-system","timestamp":"2025-08-04 13:05:50"}
{"level":"error","message":"Cloudinary upload error:","service":"college-management-system","timestamp":"2025-08-04 14:18:32"}
{"level":"error","message":"File upload failed - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials - ::1","service":"college-management-system","timestamp":"2025-08-04 14:18:32"}
