{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 13:07:56"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 13:07:56"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 13:07:56"}
{"level":"info","message":"Received SIGINT. Starting graceful shutdown...","service":"college-management-system","timestamp":"2025-07-14 13:08:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 13:54:56"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 13:54:56"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 13:54:56"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 13:56:56"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 13:56:56"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 13:56:56"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:07:01"}
{"level":"warn","message":"⚠️ Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 14:07:01"}
{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:01"}
{"level":"info","message":"⏳ Retrying in 1000ms...","service":"college-management-system","timestamp":"2025-07-14 14:07:01"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:15)\n    at startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:15)\n    at Object.<anonymous> (D:\\techvritti\\Collegemanagement\\Backend\\server.js:71:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)","timestamp":"2025-07-14 14:07:01"}
{"level":"info","message":"Database connection attempt 2/5","service":"college-management-system","timestamp":"2025-07-14 14:07:02"}
{"level":"warn","message":"⚠️ Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 14:07:02"}
{"level":"error","message":"❌ Database connection attempt 2 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:02"}
{"level":"info","message":"⏳ Retrying in 2000ms...","service":"college-management-system","timestamp":"2025-07-14 14:07:02"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:02"}
{"level":"info","message":"Database connection attempt 3/5","service":"college-management-system","timestamp":"2025-07-14 14:07:04"}
{"level":"warn","message":"⚠️ Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 14:07:04"}
{"level":"error","message":"❌ Database connection attempt 3 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:04"}
{"level":"info","message":"⏳ Retrying in 4000ms...","service":"college-management-system","timestamp":"2025-07-14 14:07:04"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:04"}
{"level":"info","message":"Database connection attempt 4/5","service":"college-management-system","timestamp":"2025-07-14 14:07:08"}
{"level":"warn","message":"⚠️ Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 14:07:08"}
{"level":"error","message":"❌ Database connection attempt 4 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:08"}
{"level":"info","message":"⏳ Retrying in 8000ms...","service":"college-management-system","timestamp":"2025-07-14 14:07:08"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:08"}
{"level":"info","message":"Database connection attempt 5/5","service":"college-management-system","timestamp":"2025-07-14 14:07:16"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:09:58"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"📋 Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"   • Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"   • Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"   • SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"   • Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"   • Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:10:34"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"📋 Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"   • Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"   • Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"   • SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"   • Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"   • Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:11:25"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"📋 Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"   • Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"   • Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"   • SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"   • Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"   • Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:12:45"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"::1 - - [14/Jul/2025:08:43:30 +0000] \"GET /health HTTP/1.1\" 200 102 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"college-management-system","timestamp":"2025-07-14 14:13:30"}
{"level":"info","message":"::1 - - [14/Jul/2025:08:43:30 +0000] \"GET /favicon.ico HTTP/1.1\" 404 110 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"college-management-system","timestamp":"2025-07-14 14:13:30"}
{"level":"info","message":"::1 - - [14/Jul/2025:08:51:14 +0000] \"GET /health/app HTTP/1.1\" 404 109 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"college-management-system","timestamp":"2025-07-14 14:21:14"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:22:40"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:23:10"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:23:28"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:23:36"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:23:38"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:24:41"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:27:02"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:30:30"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:30:38"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:31:01"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:31:41"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:32:18"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:34:30"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:37:38"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:40:54"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:40:56"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:41:24"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:45:48"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:45:53"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:48:06"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:51:19"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:52:17"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:53:36"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:54:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:56:49"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 15:01:50"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:02:00"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 15:02:00"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:02:52"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:03:04"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:04:27"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:04:35"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:05:11"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:05:13"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:11:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:11:53"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:12:06"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:15:50"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:15:52"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:18:00"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:19:01"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:23:34"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:38:09"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 15:49:12"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:49:24"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 15:49:24"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 15:49:55"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:50:46"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 15:50:46"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 15:52:07"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:52:21"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 15:52:21"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:55:14"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:55:30"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:57:33"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:58:49"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:59:26"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:59:42"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:00:29"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:00:57"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:01:29"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:03:30"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:03:30"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:03:30"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:03:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:05:41"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:05:44"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:08:16"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:13:36"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:13:45"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"::1 - - [14/Jul/2025:10:48:26 +0000] \"GET / HTTP/1.1\" 200 136 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 16:18:26"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:20:38"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:24:45"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:30:51"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:31:44"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 16:37:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:40:38"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 16:40:38"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 16:41:39"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:42:09"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 16:42:09"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 16:42:21"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:44:01"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 16:44:01"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 16:45:16"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:50:07"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 16:50:07"}
{"level":"error","message":"sendOtpEmail is not a function - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 16:55:39"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:25:39 +0000] \"POST /api/auth/register HTTP/1.1\" 500 282 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 16:55:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:56:08"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"error","message":"User already exists with this email - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 16:56:18"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:26:18 +0000] \"POST /api/auth/register HTTP/1.1\" 400 288 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 16:56:18"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:27:00 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 16:57:00"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:27:46 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 16:57:46"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:28:36 +0000] \"GET /verify-otp?email=gshit2003%40gmail.com&otp=Verify%20Your%20Email HTTP/1.1\" 404 163 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"college-management-system","timestamp":"2025-07-14 16:58:36"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:28:36 +0000] \"GET /favicon.ico HTTP/1.1\" 404 110 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"college-management-system","timestamp":"2025-07-14 16:58:36"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:03:33"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:03:35"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:34:21 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:04:21"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:37:32 +0000] \"GET /api/auth/verify-otp HTTP/1.1\" 404 118 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:07:32"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:38:00 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 83 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:08:00"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:41:10 +0000] \"GET /api/auth/login HTTP/1.1\" 404 113 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:11:10"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 17:11:30"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:41:30 +0000] \"POST /api/auth/login HTTP/1.1\" 401 256 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:11:30"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:41:40 +0000] \"POST /api/auth/login HTTP/1.1\" 200 198 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:11:40"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:13:18"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:43:50 +0000] \"GET /api/auth/me HTTP/1.1\" 401 63 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:13:50"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:16:53"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:17:06"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:17:49"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:48:31 +0000] \"POST /api/auth/login HTTP/1.1\" 200 198 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:18:31"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:48:39 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:18:39"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:48:43 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:18:43"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:51:01 +0000] \"POST /api/auth/login HTTP/1.1\" 200 198 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:21:01"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:51:08 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:21:08"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:24:18"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 17:24:50"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:26:22"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 17:26:22"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:30:39"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:30:42"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:01:22 +0000] \"POST /api/auth/login HTTP/1.1\" 200 879 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:31:22"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:03:28 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:33:28"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:38:48"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:38:51"}
{"level":"info","message":"Received SIGINT. Starting graceful shutdown...","service":"college-management-system","timestamp":"2025-07-14 17:39:36"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:39:43"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:39:44"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:09:52 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:39:52"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:09:58 +0000] \"POST /api/auth/login HTTP/1.1\" 200 879 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:39:58"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:10:02 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:40:02"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:10:18 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:40:18"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:41:09"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:41:10"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:43:44"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:43:46"}
{"level":"info","message":"Received SIGINT. Starting graceful shutdown...","service":"college-management-system","timestamp":"2025-07-14 17:45:08"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:45:14"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:45:16"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:15:27 +0000] \"POST /api/auth/login HTTP/1.1\" 200 879 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:45:27"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:15:32 +0000] \"GET /api/auth/me HTTP/1.1\" 200 683 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:45:32"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:57:26"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:57:32"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:28:45 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:58:45"}
{"level":"error","message":"Invalid OTP - POST /api/auth/verify-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 17:58:53"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:28:53 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 400 241 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:58:53"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 17:59:01"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:29:01 +0000] \"POST /api/auth/login HTTP/1.1\" 401 223 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:59:01"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:29:33 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 83 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:59:33"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:30:04 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:00:04"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:30:10 +0000] \"POST /api/auth/login HTTP/1.1\" 200 879 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:00:10"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:30:15 +0000] \"GET /api/auth/me HTTP/1.1\" 200 683 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:00:15"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:04:56"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:04:58"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:05:17"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:05:19"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:37:03 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:07:03"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 18:07:13"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:37:13 +0000] \"POST /api/auth/login HTTP/1.1\" 401 223 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:07:13"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:37:41 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 83 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:07:41"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:37:50 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 78 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:07:50"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:37:55 +0000] \"POST /api/auth/login HTTP/1.1\" 200 879 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:07:55"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:37:59 +0000] \"GET /api/auth/me HTTP/1.1\" 200 695 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:07:59"}
{"level":"error","message":"User already exists with this email - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 18:08:28"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:38:28 +0000] \"POST /api/auth/register HTTP/1.1\" 400 288 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:08:28"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:38:33 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 78 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:08:33"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:39:18 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:09:18"}
{"level":"error","message":"Invalid OTP - POST /api/auth/verify-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:10:02"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:40:02 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 400 241 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:10:02"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:40:08 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 83 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:10:08"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:40:45 +0000] \"GET /api/auth/me HTTP/1.1\" 401 42 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:10:45"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:40:52 +0000] \"POST /api/auth/login HTTP/1.1\" 200 879 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:10:52"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:40:58 +0000] \"GET /api/auth/me HTTP/1.1\" 200 695 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:10:58"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:42:24 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:12:24"}
{"level":"error","message":"Cannot destructure property 'email' of 'req.body' as it is undefined. - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:13:41"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:43:41 +0000] \"POST /api/auth/resend-otp HTTP/1.1\" 500 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:13:41"}
{"level":"error","message":"User not found - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:14:09"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:44:09 +0000] \"POST /api/auth/resend-otp HTTP/1.1\" 404 247 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:14:09"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:44:20 +0000] \"POST /api/auth/resend-otp HTTP/1.1\" 200 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:14:20"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:18:00"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:18:02"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:48:14 +0000] \"POST /api/auth/resend-otp HTTP/1.1\" 200 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:18:14"}
{"level":"error","message":"Wait 1 minute before requesting a new OTP - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:18:40"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:48:40 +0000] \"POST /api/auth/resend-otp HTTP/1.1\" 429 301 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:18:40"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:20:23"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:20:24"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:20:43"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:20:45"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:21:56"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:21:58"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:52:08 +0000] \"POST /api/auth/resend-otp HTTP/1.1\" 200 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:22:08"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:22:15"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:22:16"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:53:55 +0000] \"POST /api/auth/resend-otp HTTP/1.1\" 200 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:23:55"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:24:47"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:24:49"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:26:51"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:26:53"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:56:59 +0000] \"POST /api/auth/resend-otp HTTP/1.1\" 200 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:26:59"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:27:15"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:27:16"}
{"level":"error","message":"OTP resend limit reached for today. Try again later. - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:28:09"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:58:09 +0000] \"POST /api/auth/resend-otp HTTP/1.1\" 429 323 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:28:09"}
{"level":"info","message":"::1 - - [14/Jul/2025:12:58:42 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 83 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:28:42"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:29:39"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:29:42"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:29:57"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:29:58"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:32:33"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:32:38"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:40:39"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 18:40:40"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:14:53 +0000] \"GET /api/auth/register HTTP/1.1\" 404 116 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:44:53"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:15:50 +0000] \"GET /api/auth/register HTTP/1.1\" 404 116 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:45:50"}
{"level":"error","message":"User already exists with this email - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 18:46:41"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:16:41 +0000] \"POST /api/auth/register HTTP/1.1\" 400 288 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:46:41"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:17:05 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:47:05"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 18:47:26"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:17:26 +0000] \"POST /api/auth/login HTTP/1.1\" 401 173 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:47:26"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 18:47:42"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:17:42 +0000] \"POST /api/auth/login HTTP/1.1\" 401 223 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:47:42"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:17:53 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 83 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:47:53"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:17:59 +0000] \"POST /api/auth/login HTTP/1.1\" 200 879 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:47:59"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:18:06 +0000] \"GET /api/auth/me HTTP/1.1\" 200 569 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:48:06"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:18:39 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:48:39"}
{"level":"error","message":"Wait 1 minute before requesting a new OTP - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:48:55"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:18:55 +0000] \"POST /api/auth/resend-otp HTTP/1.1\" 429 301 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:48:55"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:20:07 +0000] \"POST /api/auth/resend-otp HTTP/1.1\" 200 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:50:07"}
{"level":"info","message":"::1 - - [14/Jul/2025:13:20:41 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 83 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 18:50:41"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 18:51:20"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 09:44:07"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 09:44:09"}
{"level":"info","message":"Received SIGINT. Starting graceful shutdown...","service":"college-management-system","timestamp":"2025-07-15 09:44:12"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 09:44:33"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 09:44:36"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-15 09:50:23"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 09:50:34"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-15 09:50:34"}
{"level":"info","message":"::1 - - [15/Jul/2025:04:30:45 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 10:00:45"}
{"level":"info","message":"::1 - - [15/Jul/2025:04:30:53 +0000] \"POST /api/auth/login HTTP/1.1\" 200 879 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 10:00:53"}
{"level":"info","message":"::1 - - [15/Jul/2025:04:30:57 +0000] \"GET /api/auth/me HTTP/1.1\" 200 614 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 10:00:57"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 10:17:42"}
