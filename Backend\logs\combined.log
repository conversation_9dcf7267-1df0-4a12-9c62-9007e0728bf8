{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 13:07:56"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 13:07:56"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 13:07:56"}
{"level":"info","message":"Received SIGINT. Starting graceful shutdown...","service":"college-management-system","timestamp":"2025-07-14 13:08:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 13:54:56"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 13:54:56"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 13:54:56"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 13:56:56"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 13:56:56"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 13:56:56"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:07:01"}
{"level":"warn","message":"⚠️ Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 14:07:01"}
{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:01"}
{"level":"info","message":"⏳ Retrying in 1000ms...","service":"college-management-system","timestamp":"2025-07-14 14:07:01"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:15)\n    at startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:15)\n    at Object.<anonymous> (D:\\techvritti\\Collegemanagement\\Backend\\server.js:71:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)","timestamp":"2025-07-14 14:07:01"}
{"level":"info","message":"Database connection attempt 2/5","service":"college-management-system","timestamp":"2025-07-14 14:07:02"}
{"level":"warn","message":"⚠️ Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 14:07:02"}
{"level":"error","message":"❌ Database connection attempt 2 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:02"}
{"level":"info","message":"⏳ Retrying in 2000ms...","service":"college-management-system","timestamp":"2025-07-14 14:07:02"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:02"}
{"level":"info","message":"Database connection attempt 3/5","service":"college-management-system","timestamp":"2025-07-14 14:07:04"}
{"level":"warn","message":"⚠️ Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 14:07:04"}
{"level":"error","message":"❌ Database connection attempt 3 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:04"}
{"level":"info","message":"⏳ Retrying in 4000ms...","service":"college-management-system","timestamp":"2025-07-14 14:07:04"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:04"}
{"level":"info","message":"Database connection attempt 4/5","service":"college-management-system","timestamp":"2025-07-14 14:07:08"}
{"level":"warn","message":"⚠️ Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 14:07:08"}
{"level":"error","message":"❌ Database connection attempt 4 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:08"}
{"level":"info","message":"⏳ Retrying in 8000ms...","service":"college-management-system","timestamp":"2025-07-14 14:07:08"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:08"}
{"level":"info","message":"Database connection attempt 5/5","service":"college-management-system","timestamp":"2025-07-14 14:07:16"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:09:58"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"📋 Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"   • Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"   • Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"   • SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"   • Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"   • Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:10:00"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:10:34"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"📋 Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"   • Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"   • Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"   • SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"   • Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"   • Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:10:36"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:11:25"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"📋 Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"   • Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"   • Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"   • SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"   • Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"   • Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:11:27"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:12:45"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:12:46"}
{"level":"info","message":"::1 - - [14/Jul/2025:08:43:30 +0000] \"GET /health HTTP/1.1\" 200 102 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"college-management-system","timestamp":"2025-07-14 14:13:30"}
{"level":"info","message":"::1 - - [14/Jul/2025:08:43:30 +0000] \"GET /favicon.ico HTTP/1.1\" 404 110 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"college-management-system","timestamp":"2025-07-14 14:13:30"}
{"level":"info","message":"::1 - - [14/Jul/2025:08:51:14 +0000] \"GET /health/app HTTP/1.1\" 404 109 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"college-management-system","timestamp":"2025-07-14 14:21:14"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:22:40"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:22:42"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:23:10"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:12"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:23:28"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:31"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:23:36"}
{"level":"info","message":"🔗 Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:23:38"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:23:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:24:41"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"✅ Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"📊 Database: test","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"🏠 Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:24:44"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:27:02"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:27:04"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:30:30"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:30:31"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:30:38"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"🚀 Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:30:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:31:01"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:31:03"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:31:41"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:31:44"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:32:18"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:32:20"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:34:30"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:34:33"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:37:38"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:37:40"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:40:54"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:40:56"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:41:00"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:41:24"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:41:25"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:45:48"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:45:50"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:45:53"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:45:57"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:48:06"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:48:08"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:51:19"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:51:21"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:52:17"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:52:18"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:53:36"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:53:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:54:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:54:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 14:56:49"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 14:56:52"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 15:01:50"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:02:00"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 15:02:00"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:02:52"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:02:54"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:03:04"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:03:06"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:04:27"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:04:29"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:04:35"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:04:36"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:05:11"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:05:13"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:05:14"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:11:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:11:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:11:53"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:11:55"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:12:06"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:12:07"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:15:50"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:15:52"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:15:53"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:18:00"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:18:02"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:19:01"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:19:02"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:23:34"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:23:36"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:38:09"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:38:11"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 15:49:12"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:49:24"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 15:49:24"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 15:49:55"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:50:46"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 15:50:46"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 15:52:07"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:52:21"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 15:52:21"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:55:14"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:55:15"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:55:30"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:55:31"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:57:33"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:57:35"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:58:49"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:58:50"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:59:26"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:59:27"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 15:59:42"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 15:59:43"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:00:29"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:00:30"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:00:57"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:00:59"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:01:29"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:01:31"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:03:30"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:03:30"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:03:30"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:03:31"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:03:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:03:38"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:05:41"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:05:43"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:05:44"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:05:46"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:08:16"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:08:18"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:13:36"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:13:37"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:13:45"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:13:47"}
{"level":"info","message":"::1 - - [14/Jul/2025:10:48:26 +0000] \"GET / HTTP/1.1\" 200 136 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 16:18:26"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:20:38"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:20:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:24:45"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:24:46"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:30:51"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:30:52"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:31:44"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:31:45"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 16:37:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:40:38"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 16:40:38"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 16:41:39"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:42:09"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 16:42:09"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 16:42:21"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:44:01"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 16:44:01"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-14 16:45:16"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:50:07"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-14 16:50:07"}
{"level":"error","message":"sendOtpEmail is not a function - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 16:55:39"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:25:39 +0000] \"POST /api/auth/register HTTP/1.1\" 500 282 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 16:55:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 16:56:08"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 16:56:09"}
{"level":"error","message":"User already exists with this email - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 16:56:18"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:26:18 +0000] \"POST /api/auth/register HTTP/1.1\" 400 288 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 16:56:18"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:27:00 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 16:57:00"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:27:46 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 16:57:46"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:28:36 +0000] \"GET /verify-otp?email=gshit2003%40gmail.com&otp=Verify%20Your%20Email HTTP/1.1\" 404 163 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"college-management-system","timestamp":"2025-07-14 16:58:36"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:28:36 +0000] \"GET /favicon.ico HTTP/1.1\" 404 110 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"college-management-system","timestamp":"2025-07-14 16:58:36"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:03:33"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:03:35"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:03:36"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:34:21 +0000] \"POST /api/auth/register HTTP/1.1\" 201 180 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:04:21"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:37:32 +0000] \"GET /api/auth/verify-otp HTTP/1.1\" 404 118 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:07:32"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:38:00 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 83 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:08:00"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:41:10 +0000] \"GET /api/auth/login HTTP/1.1\" 404 113 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:11:10"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 17:11:30"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:41:30 +0000] \"POST /api/auth/login HTTP/1.1\" 401 256 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:11:30"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:41:40 +0000] \"POST /api/auth/login HTTP/1.1\" 200 198 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:11:40"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:13:18"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:13:19"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:43:50 +0000] \"GET /api/auth/me HTTP/1.1\" 401 63 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:13:50"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:16:53"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:16:54"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:17:06"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:17:08"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:17:49"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:17:50"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:48:31 +0000] \"POST /api/auth/login HTTP/1.1\" 200 198 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:18:31"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:48:39 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:18:39"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:48:43 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:18:43"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:51:01 +0000] \"POST /api/auth/login HTTP/1.1\" 200 198 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:21:01"}
{"level":"info","message":"::1 - - [14/Jul/2025:11:51:08 +0000] \"GET /api/auth/me HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-14 17:21:08"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-14 17:24:18"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-14 17:24:19"}
{"level":"info","message":"::1 - - [15/Jul/2025:06:53:49 +0000] \"GET /api/auth/me HTTP/1.1\" 200 736 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:23:49"}
{"level":"info","message":"::1 - - [15/Jul/2025:06:53:55 +0000] \"GET /api/auth/me HTTP/1.1\" 200 736 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:23:55"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 12:25:47"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:25:48"}
{"level":"info","message":"::1 - - [15/Jul/2025:06:58:09 +0000] \"GET /api/admin/create-trainer HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:28:09"}
{"level":"info","message":"::1 - - [15/Jul/2025:06:58:15 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:28:15"}
{"level":"info","message":"::1 - - [15/Jul/2025:06:58:20 +0000] \"GET /api/admin/create-trainer HTTP/1.1\" 404 123 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:28:20"}
{"level":"error","message":"generateOtp is not defined - POST /api/admin/create-trainer - ::1","service":"college-management-system","timestamp":"2025-07-15 12:28:26"}
{"level":"info","message":"::1 - - [15/Jul/2025:06:58:26 +0000] \"POST /api/admin/create-trainer HTTP/1.1\" 500 198 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:28:26"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:31:56"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:02:03 +0000] \"POST /api/admin/create-trainer HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:32:03"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:02:12 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:32:12"}
{"level":"info","message":"Trainer created successfully: TRN0001 by admin: undefined","service":"college-management-system","timestamp":"2025-07-15 12:32:22"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:02:22 +0000] \"POST /api/admin/create-trainer HTTP/1.1\" 201 558 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:32:22"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-trainer - ::1","service":"college-management-system","timestamp":"2025-07-15 12:32:58"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:02:58 +0000] \"POST /api/admin/create-trainer HTTP/1.1\" 409 324 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:32:58"}
{"level":"info","message":"Trainer created successfully: TRN0001 by admin: undefined","service":"college-management-system","timestamp":"2025-07-15 12:33:11"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:03:11 +0000] \"POST /api/admin/create-trainer HTTP/1.1\" 201 556 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:33:11"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:03:56 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 78 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:33:56"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:04:16 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 83 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:34:16"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:06:04 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 12:36:04"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 12:37:25"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:37:26"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 12:37:41"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 12:37:41"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:37:41"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 12:37:41"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 12:37:41"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 12:37:41"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:37:42"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 12:37:42"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 12:37:42"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 12:37:42"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 12:37:42"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:37:42"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 12:37:42"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:37:42"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 12:37:53"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:37:54"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 12:40:19"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 12:40:20"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 13:00:45"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 13:00:46"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 16:27:06"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 16:27:09"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:57:22 +0000] \"POST /api/admin/create-college HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:27:22"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:57:30 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:27:30"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:27:40"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:57:40 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 324 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:27:40"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:27:52"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:57:52 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 324 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:27:52"}
{"level":"error","message":"User validation failed: name.last: Path `name.last` is required., name.first: Path `name.first` is required. - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:28:08"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:58:08 +0000] \"POST /api/admin/create-college HTTP/1.1\" 400 638 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:28:08"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:29:42"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:59:42 +0000] \"POST /api/admin/create-college HTTP/1.1\" 400 757 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:29:42"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 16:32:19"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 16:32:21"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:02:32 +0000] \"POST /api/admin/create-college HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:32:32"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:02:40 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:32:40"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:32:49"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:02:49 +0000] \"POST /api/admin/create-college HTTP/1.1\" 400 757 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:32:49"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:32:53"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:02:53 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 312 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:32:53"}
{"level":"info","message":"College created successfully: COL0001 by admin: undefined","service":"college-management-system","timestamp":"2025-07-15 16:33:26"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:03:26 +0000] \"POST /api/admin/create-college HTTP/1.1\" 201 753 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:33:26"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:33:52"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:03:52 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 312 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:33:52"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:34:29"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:04:29 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 312 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:34:29"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:34:37"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:04:37 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 312 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:34:37"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:34:49"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:04:49 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 312 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:34:49"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:35:03"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:05:03 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 312 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:35:03"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:39:15"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:09:15 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 312 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:39:15"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 16:49:51"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 16:49:52"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:19:56 +0000] \"POST /api/admin/create-college HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:49:56"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:20:16 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:50:16"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:20:28 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:50:28"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:20:35 +0000] \"GET /api/auth/me HTTP/1.1\" 200 567 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:50:35"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:50:44"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:20:44 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 294 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:50:44"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:51:57"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:21:57 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 294 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 16:51:57"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:02:33"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:02:34"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:33:10 +0000] \"POST /api/admin/create-college HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:03:10"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:34:07 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:04:07"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:04:12"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:34:12 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 294 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:04:12"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:05:41"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:05:43"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:09:05"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:09:06"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:39:14 +0000] \"POST /api/admin/create-college HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:09:14"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:39:19 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:09:19"}
{"level":"error","message":"E11000 duplicate key error collection: test.colleges index: collegeId_1 dup key: { collegeId: \"COL0001\" } - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:09:27"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:39:27 +0000] \"POST /api/admin/create-college HTTP/1.1\" 400 493 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:09:27"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:12:02"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:12:03"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:12:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:12:38"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:42:43 +0000] \"POST /api/admin/create-college HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:12:43"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:42:48 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:12:48"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:12:54"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:42:54 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 294 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:12:54"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:14:42"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:14:43"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:44:45 +0000] \"POST /api/admin/create-college HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:14:45"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:44:49 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:14:49"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:14:57"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:44:57 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 294 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:14:57"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:19:17"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:19:19"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:19:28"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:19:29"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:19:51"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:19:52"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:20:08"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:20:10"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:20:23"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:20:24"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:20:26"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:20:28"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:50:52 +0000] \"POST /api/admin/create-college HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:20:52"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:51:00 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:21:00"}
{"level":"error","message":"Cast to string failed for value \"{ '$regex': '^COL' }\" (type Object) at path \"collegeId\" for model \"College\" - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:21:05"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:51:05 +0000] \"POST /api/admin/create-college HTTP/1.1\" 404 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:21:05"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:23:23"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:23:25"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:53:26 +0000] \"POST /api/admin/create-college HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:23:26"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:53:32 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:23:32"}
{"level":"error","message":"Cast to string failed for value \"{ '$regex': '^COL', '$options': 'i' }\" (type Object) at path \"collegeId\" for model \"College\" - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:23:37"}
{"level":"info","message":"::1 - - [15/Jul/2025:11:53:37 +0000] \"POST /api/admin/create-college HTTP/1.1\" 404 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:23:37"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-15 17:27:26"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:28:31"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-15 17:28:31"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-15 17:32:57"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:33:07"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-15 17:33:07"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-15 17:35:06"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:38:40"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-15 17:38:40"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:40:32"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:40:34"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:10:42 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:40:42"}
{"level":"error","message":"Cast to string failed for value \"{ '$regex': '^COL' }\" (type Object) at path \"collegeId\" for model \"College\" - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:40:48"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:10:48 +0000] \"POST /api/admin/create-college HTTP/1.1\" 404 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:40:48"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:44:00"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:44:01"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:14:08 +0000] \"POST /api/admin/create-college HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:44:08"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:14:13 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:44:13"}
{"level":"info","message":"College created successfully: COL0001 by admin: undefined","service":"college-management-system","timestamp":"2025-07-15 17:44:24"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:14:24 +0000] \"POST /api/admin/create-college HTTP/1.1\" 201 828 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:44:24"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:44:57"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:14:57 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 324 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:44:57"}
{"level":"info","message":"College created successfully: COL0002 by admin: undefined","service":"college-management-system","timestamp":"2025-07-15 17:45:08"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:15:08 +0000] \"POST /api/admin/create-college HTTP/1.1\" 201 827 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:45:08"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-15 17:47:52"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:17:52 +0000] \"POST /api/auth/login HTTP/1.1\" 401 223 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:47:52"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-07-15 17:52:09"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:52:19"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-07-15 17:52:19"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:53:05"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:23:05 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 294 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:53:05"}
{"level":"info","message":"College created successfully: COL0003 by admin: undefined","service":"college-management-system","timestamp":"2025-07-15 17:53:24"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:23:24 +0000] \"POST /api/admin/create-college HTTP/1.1\" 201 832 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:53:24"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:23:54 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 83 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:53:54"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:24:18 +0000] \"POST /api/auth/login HTTP/1.1\" 200 894 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:54:18"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:24:47 +0000] \"POST /api/auth/login HTTP/1.1\" 200 862 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:54:47"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:24:54 +0000] \"POST /api/auth/login HTTP/1.1\" 200 894 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:54:54"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:25:07 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:55:07"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:25:22 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:55:22"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:56:15"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:56:17"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 17:56:27"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 17:56:29"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:27:31 +0000] \"POST /api/admin/users HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:57:31"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:27:38 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:57:38"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:27:45 +0000] \"GET /api/admin/users HTTP/1.1\" 200 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 17:57:45"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:30:40 +0000] \"GET /api/admin/users HTTP/1.1\" 200 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:00:40"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:31:54 +0000] \"POST /api/admin/report/user/6875f5107dc22525967ef78b HTTP/1.1\" 404 146 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:01:54"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:32:00 +0000] \"GET /api/admin/report/user/6875f5107dc22525967ef78b HTTP/1.1\" 404 145 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:02:00"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:02:14"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:02:15"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:32:20 +0000] \"GET /api/admin/report/user/6875f5107dc22525967ef78b HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:02:20"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:32:26 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:02:26"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:32:33 +0000] \"GET /api/admin/report/user/6875f5107dc22525967ef78b HTTP/1.1\" 200 237 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:02:33"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:04:56"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:04:58"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:34:59 +0000] \"GET /api/admin/report/user/6875f5107dc22525967ef78b HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:04:59"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:35:10 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:05:10"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:35:20 +0000] \"GET /api/admin/report/user/6875f5107dc22525967ef78b HTTP/1.1\" 200 229 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:05:20"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:35:43 +0000] \"GET /api/admin/report/user/6875f5107dc22525967ef78b HTTP/1.1\" 200 229 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:05:43"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:35:53 +0000] \"GET /api/admin/report/user/6875f5107dc22525967ef78b HTTP/1.1\" 200 229 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:05:53"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:36:26 +0000] \"GET /api/admin/report/user/6875f8277dc22525967ef792 HTTP/1.1\" 200 229 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:06:26"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:07:46"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:07:48"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:09:51"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:09:52"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:40:05 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:10:05"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:40:16 +0000] \"GET /api/admin/report/user/6875f8277dc22525967ef792 HTTP/1.1\" 200 229 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:10:16"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:11:46"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:11:49"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:42:07 +0000] \"GET /api/admin/report/user/6875f8277dc22525967ef792 HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:12:07"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:42:14 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:12:14"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:42:22 +0000] \"GET /api/admin/report/user/6875f8277dc22525967ef792 HTTP/1.1\" 200 408 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:12:22"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:43:48 +0000] \"GET /api/admin/report/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 200 365 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:13:48"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:15:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:15:38"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:17:04"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:17:05"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:47:45 +0000] \"GET /api/admin/report/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:17:45"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:47:51 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:17:51"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:47:58 +0000] \"GET /api/admin/report/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 200 365 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:17:58"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:48:12 +0000] \"DELETE /api/admin/report/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 404 148 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:18:12"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:18:21"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:18:22"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:48:26 +0000] \"DELETE /api/admin/report/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:18:26"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:48:31 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:18:31"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:48:38 +0000] \"DELETE /api/admin/report/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 404 148 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:18:38"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:48:52 +0000] \"DELETE /api/admin/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 200 644 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:18:52"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:49:14 +0000] \"GET /api/admin/report/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 200 365 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:19:14"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:49:18 +0000] \"GET /api/admin/report/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 200 365 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:19:18"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:49:47 +0000] \"DELETE /api/admin/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 200 644 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:19:47"}
{"level":"error","message":"Course not found - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:26:32"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:56:32 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 404 252 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:26:32"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:26:42"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:56:42 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 400 757 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:26:42"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:27:35"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:27:36"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:57:46 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:27:46"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:57:54 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:27:54"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:28:00"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:58:00 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 400 757 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:28:00"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:29:00"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:29:05"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:59:08 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:29:08"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:59:13 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:29:13"}
{"level":"error","message":"Course not found - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:29:19"}
{"level":"info","message":"::1 - - [15/Jul/2025:12:59:19 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 404 252 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:29:19"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:29:52"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:29:55"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:30:34"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:30:35"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:30:50"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:30:51"}
{"level":"info","message":"::1 - - [15/Jul/2025:13:00:58 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:30:58"}
{"level":"error","message":"Batch validation failed: department: Path `department` is required., status: `active` is not a valid enum value for path `status`. - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:31:03"}
{"level":"info","message":"::1 - - [15/Jul/2025:13:01:03 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 400 684 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:31:03"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:34:41"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:34:44"}
{"level":"info","message":"::1 - - [15/Jul/2025:13:05:25 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:35:25"}
{"level":"info","message":"::1 - - [15/Jul/2025:13:05:33 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:35:33"}
{"level":"error","message":"Batch validation failed: status: `active` is not a valid enum value for path `status`. - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:35:38"}
{"level":"info","message":"::1 - - [15/Jul/2025:13:05:39 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 400 607 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:35:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:36:42"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:36:43"}
{"level":"info","message":"::1 - - [15/Jul/2025:13:06:49 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:36:49"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:36:58"}
{"level":"info","message":"::1 - - [15/Jul/2025:13:06:58 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 500 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:36:58"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:39:15"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:39:16"}
{"level":"info","message":"::1 - - [15/Jul/2025:13:09:25 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:39:25"}
{"level":"info","message":"Batch created successfully: BAT0002 by admin: undefined","service":"college-management-system","timestamp":"2025-07-15 18:39:33"}
{"level":"info","message":"::1 - - [15/Jul/2025:13:09:33 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 201 330 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:39:33"}
{"level":"info","message":"Batch created successfully: BAT0003 by admin: undefined","service":"college-management-system","timestamp":"2025-07-15 18:40:31"}
{"level":"info","message":"::1 - - [15/Jul/2025:13:10:31 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 201 330 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-07-15 18:40:31"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:42:05"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:42:06"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:42:19"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:42:20"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-07-15 18:48:32"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-07-15 18:48:34"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 09:47:00"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:47:01"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 09:49:08"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 09:49:09"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:49:09"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 09:49:09"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 09:49:09"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 09:49:09"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:49:09"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 09:49:09"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-08-04 09:49:09"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 09:49:09"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 09:49:09"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:49:10"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 09:49:10"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:49:10"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 09:49:55"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:49:56"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 09:50:22"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:50:23"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:56:04"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 09:56:05"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:56:05"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 09:56:42"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:56:43"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 09:58:07"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 09:58:08"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 10:00:20"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":"    >Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":"    >Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":"    >Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":"    >SSL: false","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":"    >Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":"    >Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:00:21"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 10:16:19"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:16:20"}
{"level":"info","message":"::1 - - [04/Aug/2025:04:49:06 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:19:06"}
{"level":"info","message":"Batch created successfully: BAT0004 by admin: undefined","service":"college-management-system","timestamp":"2025-08-04 10:21:06"}
{"level":"info","message":"::1 - - [04/Aug/2025:04:51:06 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 201 330 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:21:06"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 10:22:45"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:22:47"}
{"level":"info","message":"::1 - - [04/Aug/2025:04:52:58 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:22:58"}
{"level":"info","message":"::1 - - [04/Aug/2025:04:53:09 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:23:09"}
{"level":"info","message":"Batch created successfully: BAT0005 by admin: undefined","service":"college-management-system","timestamp":"2025-08-04 10:23:20"}
{"level":"info","message":"::1 - - [04/Aug/2025:04:53:20 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 201 361 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:23:20"}
{"level":"info","message":"::1 - - [04/Aug/2025:04:54:02 +0000] \"GET /api/admin/users HTTP/1.1\" 200 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:24:02"}
{"level":"info","message":"::1 - - [04/Aug/2025:04:56:38 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:26:38"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 10:26:48"}
{"level":"info","message":"::1 - - [04/Aug/2025:04:56:48 +0000] \"POST /api/auth/login HTTP/1.1\" 401 173 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:26:48"}
{"level":"info","message":"::1 - - [04/Aug/2025:04:59:22 +0000] \"POST /api/auth/login HTTP/1.1\" 200 862 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:29:22"}
{"level":"error","message":"Forbidden - You do not have permission to access this resource - GET /api/admin/report/user/6875fcfaa9639a2d69b063af - ::1","service":"college-management-system","timestamp":"2025-08-04 10:32:25"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:02:25 +0000] \"GET /api/admin/report/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 403 951 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:32:25"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:02:34 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:32:34"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:02:40 +0000] \"GET /api/admin/report/user/6875fcfaa9639a2d69b063af HTTP/1.1\" 200 365 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:32:40"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 10:37:04"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:37:05"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:16:59 +0000] \"POST /api/admin/assign-batch HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:46:59"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:17:10 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:47:10"}
{"level":"error","message":"Cannot read properties of undefined (reading 'includes') - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 10:47:14"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:17:14 +0000] \"POST /api/admin/assign-batch HTTP/1.1\" 500 336 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:47:14"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 10:48:42"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:48:43"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:18:51 +0000] \"POST /api/admin/assign-batch HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:48:51"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:18:57 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:48:57"}
{"level":"error","message":"Cannot read properties of undefined (reading 'includes') - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 10:49:03"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:19:03 +0000] \"POST /api/admin/assign-batch HTTP/1.1\" 500 336 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:49:03"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 10:53:30"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:53:31"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:23:37 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:53:37"}
{"level":"error","message":"Batch validation failed: batchName: Path `batchName` is required. - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 10:53:41"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:23:41 +0000] \"POST /api/admin/assign-batch HTTP/1.1\" 400 562 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:53:41"}
{"level":"info","message":"Trainer 6875fcfaa9639a2d69b063af assigned to batch BAT0005 by admin: undefined","service":"college-management-system","timestamp":"2025-08-04 10:54:00"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:24:00 +0000] \"POST /api/admin/assign-batch HTTP/1.1\" 200 516 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:54:00"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 10:55:52"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:55:53"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:26:23 +0000] \"POST /api/admin/batches HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:56:23"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:26:32 +0000] \"GET /api/admin/batches HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:56:32"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:26:37 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:56:37"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 10:56:44"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:26:44 +0000] \"GET /api/admin/batches HTTP/1.1\" 500 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 10:56:44"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 10:59:50"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 10:59:51"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:30:01 +0000] \"GET /api/admin/batches HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:00:01"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:30:10 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:00:10"}
{"level":"error","message":"Cannot populate path `trainers` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 11:00:15"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:30:15 +0000] \"GET /api/admin/batches HTTP/1.1\" 500 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:00:15"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:00:42"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:00:43"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:30:49 +0000] \"GET /api/admin/batches HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:00:49"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:30:53 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:00:53"}
{"level":"error","message":"Trainer is already assigned to this batch - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 11:00:58"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:30:58 +0000] \"POST /api/admin/assign-batch HTTP/1.1\" 409 302 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:00:58"}
{"level":"error","message":"Cannot populate path `users` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 11:01:13"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:31:13 +0000] \"GET /api/admin/batches HTTP/1.1\" 500 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:01:13"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-08-04 11:04:13"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:04:24"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-08-04 11:04:24"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:04:54"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:04:56"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:35:00 +0000] \"GET /api/admin/batches HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:05:00"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:35:04 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:05:04"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:35:09 +0000] \"GET /api/admin/batches HTTP/1.1\" 200 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:05:09"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:06:39"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:06:40"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:37:44 +0000] \"POST /api/admin/report/batch/BAT0003 HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:07:44"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:37:51 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:07:51"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/report/batch/BAT0003 - ::1","service":"college-management-system","timestamp":"2025-08-04 11:07:57"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:37:57 +0000] \"GET /api/admin/report/batch/BAT0003 HTTP/1.1\" 500 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:07:57"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:16:40"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:16:41"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:48:51 +0000] \"POST /api/admin/create-college HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:18:51"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:48:55 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:18:55"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-08-04 11:19:06"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:49:06 +0000] \"POST /api/admin/create-college HTTP/1.1\" 409 324 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:19:06"}
{"level":"info","message":"College created successfully: COL0004 by admin: undefined","service":"college-management-system","timestamp":"2025-08-04 11:21:27"}
{"level":"info","message":"::1 - - [04/Aug/2025:05:51:27 +0000] \"POST /api/admin/create-college HTTP/1.1\" 201 776 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 11:21:27"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-08-04 11:36:02"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:36:13"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-08-04 11:36:13"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:37:19"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:37:21"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:38:20"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:38:21"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:39:00"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:39:01"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:39:24"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:39:25"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:40:03"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:40:05"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:40:41"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:40:42"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:40:57"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:40:58"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:41:11"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:41:12"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-08-04 11:45:02"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:48:58"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-08-04 11:48:58"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-08-04 11:49:38"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:50:59"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-08-04 11:50:59"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-08-04 11:51:40"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:52:16"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-08-04 11:52:16"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-08-04 11:52:36"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:54:09"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-08-04 11:54:09"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:58:49"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:58:51"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 11:59:29"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 11:59:30"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:00:11"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:00:12"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:01:05"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:01:19"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:01:20"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:14:02"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:14:03"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:15:34"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:15:35"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:21:22"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:21:24"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:21:56"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:21:57"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:22:25"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:22:26"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:22:44"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:22:45"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:22:47"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:22:49"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:24:38"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:24:39"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:32:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:32:38"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:33:40"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:33:41"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:34:05"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:04:05 +0000] \"POST /api/auth/login HTTP/1.1\" 401 173 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:34:05"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:04:38 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:34:38"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:05:26 +0000] \"POST /api/trainers/batches HTTP/1.1\" 404 120 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:35:26"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 12:35:36"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:05:36 +0000] \"GET /api/trainers/batches HTTP/1.1\" 500 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:35:36"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:37:42"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:37:46"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:37:53"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:37:55"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:07:56 +0000] \"GET /api/trainers/batches HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:37:56"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:08:02 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:38:02"}
{"level":"error","message":"Cannot populate path `college` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 12:38:06"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:08:06 +0000] \"GET /api/trainers/batches HTTP/1.1\" 500 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:38:06"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:38:17"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:38:19"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:08:21 +0000] \"GET /api/trainers/batches HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:38:21"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:08:26 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:38:26"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:08:32 +0000] \"GET /api/trainers/batches HTTP/1.1\" 200 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:38:32"}
{"level":"error","message":"Forbidden - You do not have permission to access this resource - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 12:40:46"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:10:46 +0000] \"POST /api/admin/assign-batch HTTP/1.1\" 403 951 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:40:46"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:10:52 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:40:52"}
{"level":"info","message":"Trainer 6875fd2ca9639a2d69b063b6 assigned to batch BAT0005 by admin: undefined","service":"college-management-system","timestamp":"2025-08-04 12:40:57"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:10:57 +0000] \"POST /api/admin/assign-batch HTTP/1.1\" 200 514 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:40:57"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:11:29 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:41:29"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:11:33 +0000] \"GET /api/trainers/batches HTTP/1.1\" 200 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:41:33"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:43:07"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:13:10 +0000] \"GET /api/trainers/batches HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:43:10"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:13:15 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:43:15"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 12:43:20"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:13:20 +0000] \"GET /api/trainers/batches HTTP/1.1\" 500 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:43:20"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:46:48"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:46:50"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:16:55 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:46:55"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:16:59 +0000] \"GET /api/trainers/batches HTTP/1.1\" 200 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:46:59"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:18:27 +0000] \"POST /api/auth/login HTTP/1.1\" 200 871 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:48:27"}
{"level":"info","message":"Batch created successfully: BAT0006 by admin: undefined","service":"college-management-system","timestamp":"2025-08-04 12:48:40"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:18:40 +0000] \"POST /api/admin/create-batch HTTP/1.1\" 201 361 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:48:40"}
{"level":"info","message":"Trainer 6875fd2ca9639a2d69b063b6 assigned to batch BAT0006 by admin: undefined","service":"college-management-system","timestamp":"2025-08-04 12:48:56"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:18:56 +0000] \"POST /api/admin/assign-batch HTTP/1.1\" 200 514 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:48:56"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:49:53"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:19:53 +0000] \"POST /api/auth/login HTTP/1.1\" 401 223 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:49:53"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:20:47 +0000] \"POST /api/auth/login HTTP/1.1\" 200 893 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:50:47"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:20:53 +0000] \"GET /api/trainers/batches HTTP/1.1\" 200 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:50:53"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:52:37"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:52:41"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:22:44 +0000] \"POST /api/auth/login HTTP/1.1\" 200 893 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:52:44"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:22:49 +0000] \"GET /api/trainers/batches HTTP/1.1\" 200 200 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:52:49"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:53:14"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:23:14 +0000] \"POST /api/auth/login HTTP/1.1\" 401 173 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:53:14"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:53:21"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:23:21 +0000] \"POST /api/auth/login HTTP/1.1\" 401 173 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:53:21"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:23:41 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:53:41"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:23:45 +0000] \"GET /api/trainers/batches HTTP/1.1\" 200 841 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:53:45"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:54:31"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:54:34"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:25:05 +0000] \"POST /api/trainers/batches/687652713d110f073072eed1 HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:55:05"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:25:11 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:55:11"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:25:14 +0000] \"POST /api/trainers/batches/687652713d110f073072eed1 HTTP/1.1\" 404 145 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:55:14"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches/687652713d110f073072eed1 - ::1","service":"college-management-system","timestamp":"2025-08-04 12:55:20"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:25:20 +0000] \"GET /api/trainers/batches/687652713d110f073072eed1 HTTP/1.1\" 500 - \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:55:20"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:56:15"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:56:19"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:26:21 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:56:21"}
{"level":"error","message":"Batch not found or not assigned to you - GET /api/trainers/batches/687652713d110f073072eed1 - ::1","service":"college-management-system","timestamp":"2025-08-04 12:56:25"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:26:25 +0000] \"GET /api/trainers/batches/687652713d110f073072eed1 HTTP/1.1\" 404 298 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:56:25"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:26:59 +0000] \"GET /api/trainers/batches/68905ed023a8a6a92f6d54a5 HTTP/1.1\" 200 504 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:56:59"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:27:44 +0000] \"GET /api/trainers/batches/68905ed023a8a6a92f6d54a5/students HTTP/1.1\" 200 93 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:57:44"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:58:00"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:58:01"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:28:08 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:58:08"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:28:12 +0000] \"GET /api/trainers/batches/68905ed023a8a6a92f6d54a5/students HTTP/1.1\" 200 93 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:58:12"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:28:36 +0000] \"GET /api/trainers/batches/68905ed023a8a6a92f6d54a5/students HTTP/1.1\" 200 93 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:58:36"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:29:03 +0000] \"GET /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials HTTP/1.1\" 200 95 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 12:59:03"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:59:38"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:59:44"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:59:50"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:59:51"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 12:59:56"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 12:59:58"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 13:00:30"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 13:00:31"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:31:30 +0000] \"GET /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials HTTP/1.1\" 401 79 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 13:01:30"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:31:35 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 13:01:35"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:31:39 +0000] \"GET /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials HTTP/1.1\" 200 95 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 13:01:39"}
{"level":"error","message":"Cloudinary upload error:","service":"college-management-system","timestamp":"2025-08-04 13:05:50"}
{"level":"error","message":"File upload failed - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials - ::1","service":"college-management-system","timestamp":"2025-08-04 13:05:50"}
{"level":"info","message":"::1 - - [04/Aug/2025:07:35:50 +0000] \"POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials HTTP/1.1\" 500 361 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 13:05:50"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-08-04 13:57:42"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 13:57:54"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-08-04 13:57:54"}
{"level":"warn","message":" ---Mongoose disconnected from database","service":"college-management-system","timestamp":"2025-08-04 13:58:24"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 13:58:34"}
{"level":"info","message":" Mongoose reconnected to database","service":"college-management-system","timestamp":"2025-08-04 13:58:34"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-01.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 14:17:12"}
{"level":"info","message":"::1 - - [04/Aug/2025:08:48:28 +0000] \"POST /api/auth/login HTTP/1.1\" 200 885 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 14:18:28"}
{"level":"error","message":"Cloudinary upload error:","service":"college-management-system","timestamp":"2025-08-04 14:18:32"}
{"level":"error","message":"File upload failed - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials - ::1","service":"college-management-system","timestamp":"2025-08-04 14:18:32"}
{"level":"info","message":"::1 - - [04/Aug/2025:08:48:32 +0000] \"POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials HTTP/1.1\" 500 361 \"-\" \"PostmanRuntime/7.44.1\"","service":"college-management-system","timestamp":"2025-08-04 14:18:32"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 14:24:59"}
{"level":"info","message":"Database connection attempt 1/5","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":"<+> Mongoose connected to database","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":" Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":" Database: test","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":"Host: ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":">>>>Connection Configuration:","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":">>>>Environment: development","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":">>>>Pool Size: 10","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":">>>>SSL: false","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":">>>>Auth Source: default","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":">>>>Replica Set: none","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":"Database connected successfully","service":"college-management-system","timestamp":"2025-08-04 14:27:30"}
{"level":"info","message":"Server running on http://localhost:5000","service":"college-management-system","timestamp":"2025-08-04 14:27:31"}
{"level":"info","message":"Environment: development","service":"college-management-system","timestamp":"2025-08-04 14:27:31"}
