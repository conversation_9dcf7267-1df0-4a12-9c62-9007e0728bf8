const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const { protect } = require('../middlewares/authMiddleware');
const roleMiddleware = require('../middlewares/roleMiddleware');
const { upload } = require('../middlewares/fileUpload');
const {
    validateCreateStudent,
    validateCreateTrainer,
    validateCreateCollege,
    validateCreateBatch,
    validateAssignBatch,
    validateUploadCourse,
    validateUploadCertificate
} = require('../middlewares/validation');

// Apply authentication and admin role middleware to all routes
router.use(protect, roleMiddleware('admin'));

// User Management
router.post('/create-student', validateCreateStudent, adminController.createStudent);
router.post('/create-trainer', validateCreateTrainer, adminController.createTrainer);
router.post('/create-college', validateCreateCollege, adminController.createCollege);
router.get('/users', adminController.getAllUsers);
router.get('/report/user/:userId', adminController.getUserReport);
router.patch('/user/:userId/toggle-status', adminController.toggleUserStatus);
router.patch('/user/:userId', adminController.updateUser);
router.delete('/user/:userId', adminController.deleteUser);

// Batch Management
router.post('/create-batch', validateCreateBatch, adminController.createBatch);
router.post('/assign-batch', validateAssignBatch, adminController.assignBatchToTrainer);
router.get('/batches', adminController.getAllBatches);
router.get('/report/batch/:batchId', adminController.getBatchReport);
router.patch('/batch/:batchId/status', adminController.updateBatchStatus);
router.delete('/batch/:batchId/student/:studentId', adminController.removeStudentFromBatch);

// Course Management
router.post('/upload-course', upload.single('courseFile'), validateUploadCourse, adminController.uploadCourse);
router.get('/courses', adminController.getAllCourses);

// Certificate Management
router.post('/upload-certificate', upload.single('certificateFile'), validateUploadCertificate, adminController.uploadCertificate);

// Dashboard
router.get('/dashboard/stats', adminController.getDashboardStats);
router.get('/dashboard/recent-activities', adminController.getRecentActivities);

// Bulk Operations
router.post('/bulk/create-students', upload.single('csvFile'), adminController.bulkCreateStudents);
router.post('/bulk/assign-students', adminController.bulkAssignStudents);

// Reports and Analytics
router.get('/report/department-wise', adminController.getDepartmentWiseReport);
router.get('/report/batch-performance', adminController.getBatchPerformanceReport);
router.get('/report/monthly-registrations', adminController.getMonthlyRegistrationReport);

module.exports = router;