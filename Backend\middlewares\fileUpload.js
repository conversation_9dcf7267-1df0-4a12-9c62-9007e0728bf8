// const createCsvUpload = () => {
//     return multer({
//         storage: storageConfigs.general, // or createCsvStorage()
//         fileFilter: (req, file, cb) => {
//             if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
//                 cb(null, true);
//             } else {
//                 cb(new Error('Only CSV files are allowed'), false);
//             }
//         }
//     });
// };
const {
    uploadMiddleware: cloudinaryUploadMiddleware,
    fileSizeLimits,
    uploadToCloudinary,
    deleteFromCloudinary,
    generateSignedUrl,
    getFileInfo,
    testConnection,
    // csv: createCsvUpload()
} = require('../config/cloudiinary'); // adjust path as needed

module.exports = {
    uploadMiddleware: cloudinaryUploadMiddleware, // Main upload middleware
    uploadToCloudinary,
    deleteFromCloudinary,
    generateSignedUrl,
    getFileInfo,
    testConnection,
    fileSizeLimits
};
