const User = require('../models/User');
const Batch = require('../models/Batch');
const Course = require('../models/Course');
const Certificate = require('../models/Certificate');
const ApiResponse = require('../utils/apiResponse');
const ApiError = require('../utils/apiError');

// Create Student
exports.createStudent = async (req, res, next) => {
    const student = await User.create({ ...req.body, role: 'student' });
    res.status(201).json(new ApiResponse(201, student, 'Student created'));
};

// Create Trainer
exports.createTrainer = async (req, res, next) => {
    const trainer = await User.create({ ...req.body, role: 'trainer' });
    res.status(201).json(new ApiResponse(201, trainer, 'Trainer created'));
};

// Create College
exports.createCollege = async (req, res, next) => {
    const college = await User.create({ ...req.body, role: 'college' });
    res.status(201).json(new ApiResponse(201, college, 'College created'));
};

// Create Batch
exports.createBatch = async (req, res, next) => {
    const batch = await Batch.create(req.body);
    res.status(201).json(new ApiResponse(201, batch, 'Batch created'));
};

// Assign Batch to Trainer
exports.assignBatchToTrainer = async (req, res, next) => {
    const { batchId, trainerId } = req.body;
    const trainer = await User.findById(trainerId);
    if (!trainer || trainer.role !== 'trainer') {
        throw new ApiError(400, 'Invalid trainer');
    }

    trainer.assignedBatches = trainer.assignedBatches || [];
    trainer.assignedBatches.push(batchId);
    await trainer.save();

    res.status(200).json(new ApiResponse(200, {}, 'Batch assigned to trainer'));
};

// Upload Course
exports.uploadCourse = async (req, res, next) => {
    const course = await Course.create(req.body);
    res.status(201).json(new ApiResponse(201, course, 'Course uploaded'));
};

// Upload Certificate
exports.uploadCertificate = async (req, res, next) => {
    const certificate = await Certificate.create(req.body);
    res.status(201).json(new ApiResponse(201, certificate, 'Certificate uploaded'));
};

// View Batch Report
exports.getBatchReport = async (req, res, next) => {
    const batchId = req.params.batchId;
    const students = await User.find({ batchId, role: 'student' });
    res.status(200).json(new ApiResponse(200, students, 'Batch report'));
};

// View User Report
exports.getUserReport = async (req, res, next) => {
    const userId = req.params.userId;
    const user = await User.findById(userId).populate('courses');
    res.status(200).json(new ApiResponse(200, user, 'User report'));
};
