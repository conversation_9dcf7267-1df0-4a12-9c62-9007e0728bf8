// const User = require('../models/User');
// const Batch = require('../models/Batch');
// const Course = require('../models/Course');
// const Certificate = require('../models/Certificate');
// const ApiResponse = require('../utils/apiResponse');
// const ApiError = require('../utils/apiError');

// // Create Student
// exports.createStudent = async (req, res, next) => {
//     const student = await User.create({ ...req.body, role: 'student' });
//     res.status(201).json(new ApiResponse(201, student, 'Student created'));
// };

// // Create Trainer
// exports.createTrainer = async (req, res, next) => {
//     const trainer = await User.create({ ...req.body, role: 'trainer' });
//     res.status(201).json(new ApiResponse(201, trainer, 'Trainer created'));
// };

// // Create College
// exports.createCollege = async (req, res, next) => {
//     const college = await User.create({ ...req.body, role: 'college' });
//     res.status(201).json(new ApiResponse(201, college, 'College created'));
// };

// // Create Batch
// exports.createBatch = async (req, res, next) => {
//     const batch = await Batch.create(req.body);
//     res.status(201).json(new ApiResponse(201, batch, 'Batch created'));
// };

// // Assign Batch to Trainer
// exports.assignBatchToTrainer = async (req, res, next) => {
//     const { batchId, trainerId } = req.body;
//     const trainer = await User.findById(trainerId);
//     if (!trainer || trainer.role !== 'trainer') {
//         throw new ApiError(400, 'Invalid trainer');
//     }

//     trainer.assignedBatches = trainer.assignedBatches || [];
//     trainer.assignedBatches.push(batchId);
//     await trainer.save();

//     res.status(200).json(new ApiResponse(200, {}, 'Batch assigned to trainer'));
// };

// // Upload Course
// exports.uploadCourse = async (req, res, next) => {
//     const course = await Course.create(req.body);
//     res.status(201).json(new ApiResponse(201, course, 'Course uploaded'));
// };

// // Upload Certificate
// exports.uploadCertificate = async (req, res, next) => {
//     const certificate = await Certificate.create(req.body);
//     res.status(201).json(new ApiResponse(201, certificate, 'Certificate uploaded'));
// };

// // View Batch Report
// exports.getBatchReport = async (req, res, next) => {
//     const batchId = req.params.batchId;
//     const students = await User.find({ batchId, role: 'student' });
//     res.status(200).json(new ApiResponse(200, students, 'Batch report'));
// };

// // View User Report
// exports.getUserReport = async (req, res, next) => {
//     const userId = req.params.userId;
//     const user = await User.findById(userId).populate('courses');
//     res.status(200).json(new ApiResponse(200, user, 'User report'));
// };

const asyncHandler = require('express-async-handler');
const ApiError = require('../utils/apiError');
const ApiResponse = require('../utils/apiResponse');
const User = require('../models/User');
const Batch = require('../models/Batch');
const Course = require('../models/Course');
const College = require('../models/College');
const Certificate = require('../models/Certificate');
const bcrypt = require('bcryptjs');
const { generateUserId } = require('../utils/helpers');
const { uploadFile } = require('../utils/fileUpload');
const logger = require('../utils/logger');
const csvParser = require('csv-parser');
const fs = require('fs');

// ==================== USER MANAGEMENT ====================

/**
 * Create Student
 * @route POST /api/admin/create-student
 * @access Private (Admin only)
 */
const createStudent = asyncHandler(async (req, res) => {
    const {
        firstName,
        lastName,
        email,
        mobile,
        whatsappNo,
        dateOfBirth,
        studentType, // 'student' or 'professional'
        collegeName,
        companyName,
        passingYear,
        department,
        semester,
        preferredLocation,
        password
    } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !mobile || !dateOfBirth || !studentType) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    // Check if user already exists
    const existingUser = await User.findOne({
        $or: [
            { email: email.toLowerCase() },
            { mobile }
        ]
    });

    if (existingUser) {
        throw new ApiError(409, 'User already exists with this email or mobile number');
    }

    // Generate student ID
    const studentId = await generateUserId('STU');

    // Hash password
    const hashedPassword = await bcrypt.hash(password || 'student123', 12);

    // Create student
    const student = await User.create({
        userId: studentId,
        firstName,
        lastName,
        email: email.toLowerCase(),
        mobile,
        whatsappNo: whatsappNo || mobile,
        dateOfBirth: new Date(dateOfBirth),
        role: 'student',
        studentType,
        collegeName: studentType === 'student' ? collegeName : null,
        companyName: studentType === 'professional' ? companyName : null,
        passingYear,
        department,
        semester,
        preferredLocation,
        password: hashedPassword,
        isActive: true,
        createdBy: req.user._id
    });

    // Remove password from response
    const { password: _, ...studentData } = student.toObject();

    logger.info(`Student created successfully: ${studentId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, studentData, 'Student created successfully')
    );
});

/**
 * Create Trainer
 * @route POST /api/admin/create-trainer
 * @access Private (Admin only)
 */
const createTrainer = asyncHandler(async (req, res) => {
    const {
        firstName,
        lastName,
        email,
        mobile,
        dateOfBirth,
        qualification,
        experience,
        specialization,
        preferredLocation,
        password
    } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !mobile || !qualification || !experience) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    // Check if user already exists
    const existingUser = await User.findOne({
        $or: [
            { email: email.toLowerCase() },
            { mobile }
        ]
    });

    if (existingUser) {
        throw new ApiError(409, 'User already exists with this email or mobile number');
    }

    // Generate trainer ID
    const trainerId = await generateUserId('TRN');

    // Hash password
    const hashedPassword = await bcrypt.hash(password || 'trainer123', 12);

    // Create trainer
    const trainer = await User.create({
        userId: trainerId,
        firstName,
        lastName,
        email: email.toLowerCase(),
        mobile,
        dateOfBirth: new Date(dateOfBirth),
        role: 'trainer',
        qualification,
        experience,
        specialization,
        preferredLocation,
        password: hashedPassword,
        isActive: true,
        createdBy: req.user._id
    });

    // Remove password from response
    const { password: _, ...trainerData } = trainer.toObject();

    logger.info(`Trainer created successfully: ${trainerId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, trainerData, 'Trainer created successfully')
    );
});

/**
 * Create College
 * @route POST /api/admin/create-college
 * @access Private (Admin only)
 */
const createCollege = asyncHandler(async (req, res) => {
    const {
        firstName,
        lastName,
        email,
        mobile,
        collegeName,
        collegeCode,
        address,
        city,
        state,
        departments,
        password
    } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !mobile || !collegeName || !collegeCode) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    // Check if college already exists
    const existingCollege = await College.findOne({
        $or: [
            { collegeCode: collegeCode.toUpperCase() },
            { email: email.toLowerCase() }
        ]
    });

    if (existingCollege) {
        throw new ApiError(409, 'College already exists with this code or email');
    }

    // Check if user already exists
    const existingUser = await User.findOne({
        $or: [
            { email: email.toLowerCase() },
            { mobile }
        ]
    });

    if (existingUser) {
        throw new ApiError(409, 'User already exists with this email or mobile number');
    }

    // Generate college ID
    const collegeId = await generateUserId('COL');

    // Hash password
    const hashedPassword = await bcrypt.hash(password || 'college123', 12);

    // Create college record
    const college = await College.create({
        collegeId,
        collegeName,
        collegeCode: collegeCode.toUpperCase(),
        address,
        city,
        state,
        departments: departments || [],
        isActive: true,
        createdBy: req.user._id
    });

    // Create college user account
    const collegeUser = await User.create({
        userId: collegeId,
        firstName,
        lastName,
        email: email.toLowerCase(),
        mobile,
        role: 'college',
        collegeName,
        collegeCode: collegeCode.toUpperCase(),
        password: hashedPassword,
        isActive: true,
        createdBy: req.user._id
    });

    // Remove password from response
    const { password: _, ...collegeUserData } = collegeUser.toObject();

    logger.info(`College created successfully: ${collegeId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, {
            college,
            user: collegeUserData
        }, 'College created successfully')
    );
});

/**
 * Get All Users
 * @route GET /api/admin/users
 * @access Private (Admin only)
 */
const getAllUsers = asyncHandler(async (req, res) => {
    const { role, page = 1, limit = 10, search } = req.query;

    const filter = {};
    if (role) filter.role = role;
    if (search) {
        filter.$or = [
            { firstName: { $regex: search, $options: 'i' } },
            { lastName: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { userId: { $regex: search, $options: 'i' } }
        ];
    }

    const users = await User.find(filter)
        .select('-password')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const totalUsers = await User.countDocuments(filter);

    res.status(200).json(
        new ApiResponse(200, {
            users,
            totalPages: Math.ceil(totalUsers / limit),
            currentPage: page,
            totalUsers
        }, 'Users retrieved successfully')
    );
});

/**
 * Get User Report
 * @route GET /api/admin/report/user/:userId
 * @access Private (Admin only)
 */
const getUserReport = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    // Find user
    const user = await User.findOne({ userId })
        .select('-password')
        .populate('assignedBatches', 'batchId batchName startDate endDate status');

    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    let report = {
        user: {
            userId: user.userId,
            fullName: `${user.firstName} ${user.lastName}`,
            email: user.email,
            mobile: user.mobile,
            role: user.role,
            isActive: user.isActive,
            createdAt: user.createdAt
        }
    };

    // Add role-specific information
    if (user.role === 'student') {
        report.studentDetails = {
            studentType: user.studentType,
            collegeName: user.collegeName,
            companyName: user.companyName,
            department: user.department,
            semester: user.semester,
            passingYear: user.passingYear,
            preferredLocation: user.preferredLocation
        };
    } else if (user.role === 'trainer') {
        report.trainerDetails = {
            qualification: user.qualification,
            experience: user.experience,
            specialization: user.specialization,
            assignedBatches: user.assignedBatches
        };
    } else if (user.role === 'college') {
        report.collegeDetails = {
            collegeName: user.collegeName,
            collegeCode: user.collegeCode
        };
    }

    res.status(200).json(
        new ApiResponse(200, report, 'User report retrieved successfully')
    );
});

/**
 * Toggle User Status
 * @route PATCH /api/admin/user/:userId/toggle-status
 * @access Private (Admin only)
 */
const toggleUserStatus = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    const user = await User.findOne({ userId });
    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    user.isActive = !user.isActive;
    await user.save();

    res.status(200).json(
        new ApiResponse(200, {
            userId: user.userId,
            isActive: user.isActive
        }, `User ${user.isActive ? 'activated' : 'deactivated'} successfully`)
    );
});

/**
 * Update User Information
 * @route PATCH /api/admin/user/:userId
 * @access Private (Admin only)
 */
const updateUser = asyncHandler(async (req, res) => {
    const { userId } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated
    delete updates.password;
    delete updates.userId;
    delete updates.role;
    delete updates.createdAt;
    delete updates.updatedAt;

    const user = await User.findOneAndUpdate(
        { userId },
        { ...updates, updatedAt: new Date() },
        { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    res.status(200).json(
        new ApiResponse(200, user, 'User updated successfully')
    );
});

/**
 * Delete User (Soft Delete)
 * @route DELETE /api/admin/user/:userId
 * @access Private (Admin only)
 */
const deleteUser = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    const user = await User.findOneAndUpdate(
        { userId },
        {
            isActive: false,
            deletedAt: new Date(),
            deletedBy: req.user._id
        },
        { new: true }
    ).select('-password');

    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    res.status(200).json(
        new ApiResponse(200, user, 'User deleted successfully')
    );
});

// ==================== BATCH MANAGEMENT ====================

/**
 * Create Batch
 * @route POST /api/admin/create-batch
 * @access Private (Admin only)
 */
const createBatch = asyncHandler(async (req, res) => {
    const {
        batchName,
        courseId,
        startDate,
        endDate,
        maxStudents,
        location,
        schedule,
        description
    } = req.body;

    // Validation
    if (!batchName || !courseId || !startDate || !endDate || !maxStudents) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    // Check if course exists
    const course = await Course.findById(courseId);
    if (!course) {
        throw new ApiError(404, 'Course not found');
    }

    // Generate batch ID
    const batchId = await generateUserId('BAT');

    // Create batch
    const batch = await Batch.create({
        batchId,
        batchName,
        course: courseId,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        maxStudents,
        location,
        schedule,
        description,
        status: 'active',
        createdBy: req.user._id
    });

    // Populate course details
    await batch.populate('course', 'courseName duration');

    logger.info(`Batch created successfully: ${batchId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, batch, 'Batch created successfully')
    );
});

/**
 * Assign Batch to Trainer
 * @route POST /api/admin/assign-batch
 * @access Private (Admin only)
 */
const assignBatchToTrainer = asyncHandler(async (req, res) => {
    const { batchId, trainerId } = req.body;

    // Validation
    if (!batchId || !trainerId) {
        throw new ApiError(400, 'Please provide batch ID and trainer ID');
    }

    // Check if batch exists
    const batch = await Batch.findOne({ batchId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    // Check if trainer exists
    const trainer = await User.findOne({ userId: trainerId, role: 'trainer' });
    if (!trainer) {
        throw new ApiError(404, 'Trainer not found');
    }

    // Check if trainer is already assigned to this batch
    if (batch.trainers.includes(trainer._id)) {
        throw new ApiError(409, 'Trainer is already assigned to this batch');
    }

    // Assign trainer to batch
    batch.trainers.push(trainer._id);
    await batch.save();

    // Update trainer's assigned batches
    trainer.assignedBatches = trainer.assignedBatches || [];
    trainer.assignedBatches.push(batch._id);
    await trainer.save();

    // Populate trainer details
    await batch.populate('trainers', 'userId firstName lastName email mobile');

    logger.info(`Trainer ${trainerId} assigned to batch ${batchId} by admin: ${req.user.userId}`);

    res.status(200).json(
        new ApiResponse(200, batch, 'Trainer assigned to batch successfully')
    );
});

/**
 * Get All Batches
 * @route GET /api/admin/batches
 * @access Private (Admin only)
 */
const getAllBatches = asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, status } = req.query;

    const filter = {};
    if (status) filter.status = status;

    const batches = await Batch.find(filter)
        .populate('course', 'courseName courseCode')
        .populate('trainers', 'userId firstName lastName')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const totalBatches = await Batch.countDocuments(filter);

    res.status(200).json(
        new ApiResponse(200, {
            batches,
            totalPages: Math.ceil(totalBatches / limit),
            currentPage: page,
            totalBatches
        }, 'Batches retrieved successfully')
    );
});

/**
 * Get Batch Report
 * @route GET /api/admin/report/batch/:batchId
 * @access Private (Admin only)
 */
const getBatchReport = asyncHandler(async (req, res) => {
    const { batchId } = req.params;

    // Find batch with populated data
    const batch = await Batch.findOne({ batchId })
        .populate('course', 'courseName courseCode duration')
        .populate('trainers', 'userId firstName lastName email mobile')
        .populate('students', 'userId firstName lastName email mobile department collegeName');

    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    // Get additional statistics
    const totalStudents = batch.students.length;
    const maxStudents = batch.maxStudents;
    const occupancyRate = totalStudents > 0 ? ((totalStudents / maxStudents) * 100).toFixed(2) : 0;

    const report = {
        batch: {
            batchId: batch.batchId,
            batchName: batch.batchName,
            startDate: batch.startDate,
            endDate: batch.endDate,
            status: batch.status,
            location: batch.location,
            schedule: batch.schedule
        },
        course: batch.course,
        trainers: batch.trainers,
        students: batch.students,
        statistics: {
            totalStudents,
            maxStudents,
            occupancyRate: `${occupancyRate}%`,
            availableSlots: maxStudents - totalStudents
        }
    };

    res.status(200).json(
        new ApiResponse(200, report, 'Batch report retrieved successfully')
    );
});

/**
 * Update Batch Status
 * @route PATCH /api/admin/batch/:batchId/status
 * @access Private (Admin only)
 */
const updateBatchStatus = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { status } = req.body;

    const validStatuses = ['active', 'inactive', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
        throw new ApiError(400, 'Invalid status. Valid statuses: ' + validStatuses.join(', '));
    }

    const batch = await Batch.findOneAndUpdate(
        { batchId },
        { status, updatedAt: new Date() },
        { new: true }
    ).populate('course', 'courseName courseCode');

    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    res.status(200).json(
        new ApiResponse(200, batch, 'Batch status updated successfully')
    );
});

/**
 * Remove Student from Batch
 * @route DELETE /api/admin/batch/:batchId/student/:studentId
 * @access Private (Admin only)
 */
const removeStudentFromBatch = asyncHandler(async (req, res) => {
    const { batchId, studentId } = req.params;

    const batch = await Batch.findOne({ batchId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    const student = await User.findOne({ userId: studentId });
    if (!student) {
        throw new ApiError(404, 'Student not found');
    }

    // Remove student from batch
    batch.students = batch.students.filter(
        id => !id.equals(student._id)
    );
    await batch.save();

    // Remove batch from student's assigned batches
    student.assignedBatches = student.assignedBatches.filter(
        id => !id.equals(batch._id)
    );
    await student.save();

    res.status(200).json(
        new ApiResponse(200, {
            batchId: batch.batchId,
            studentId: student.userId,
            remainingStudents: batch.students.length
        }, 'Student removed from batch successfully')
    );
});

// ==================== COURSE MANAGEMENT ====================

/**
 * Upload Course
 * @route POST /api/admin/upload-course
 * @access Private (Admin only)
 */
const uploadCourse = asyncHandler(async (req, res) => {
    const {
        courseName,
        courseCode,
        description,
        duration,
        difficulty,
        prerequisites,
        objectives,
        modules
    } = req.body;

    // Validation
    if (!courseName || !courseCode || !duration) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    // Check if course already exists
    const existingCourse = await Course.findOne({
        courseCode: courseCode.toUpperCase()
    });

    if (existingCourse) {
        throw new ApiError(409, 'Course already exists with this code');
    }

    // Handle file upload if provided
    let courseFile = null;
    if (req.file) {
        courseFile = await uploadFile(req.file, 'courses');
    }

    // Create course
    const course = await Course.create({
        courseName,
        courseCode: courseCode.toUpperCase(),
        description,
        duration,
        difficulty: difficulty || 'intermediate',
        prerequisites: prerequisites || [],
        objectives: objectives || [],
        modules: modules || [],
        courseFile,
        isActive: true,
        createdBy: req.user._id
    });

    logger.info(`Course created successfully: ${courseCode} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, course, 'Course created successfully')
    );
});

/**
 * Get All Courses
 * @route GET /api/admin/courses
 * @access Private (Admin only)
 */
const getAllCourses = asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, search } = req.query;

    const filter = {};
    if (search) {
        filter.$or = [
            { courseName: { $regex: search, $options: 'i' } },
            { courseCode: { $regex: search, $options: 'i' } }
        ];
    }

    const courses = await Course.find(filter)
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const totalCourses = await Course.countDocuments(filter);

    res.status(200).json(
        new ApiResponse(200, {
            courses,
            totalPages: Math.ceil(totalCourses / limit),
            currentPage: page,
            totalCourses
        }, 'Courses retrieved successfully')
    );
});

// ==================== CERTIFICATE MANAGEMENT ====================

/**
 * Upload Certificate
 * @route POST /api/admin/upload-certificate
 * @access Private (Admin only)
 */
const uploadCertificate = asyncHandler(async (req, res) => {
    const {
        studentId,
        batchId,
        courseId,
        certificateType,
        issueDate,
        grade,
        remarks
    } = req.body;

    // Validation
    if (!studentId || !batchId || !courseId || !certificateType) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    // Verify student exists
    const student = await User.findOne({ userId: studentId, role: 'student' });
    if (!student) {
        throw new ApiError(404, 'Student not found');
    }

    // Verify batch exists
    const batch = await Batch.findOne({ batchId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    // Verify course exists
    const course = await Course.findById(courseId);
    if (!course) {
        throw new ApiError(404, 'Course not found');
    }

    // Check if certificate already exists
    const existingCertificate = await Certificate.findOne({
        student: student._id,
        batch: batch._id,
        course: courseId
    });

    if (existingCertificate) {
        throw new ApiError(409, 'Certificate already exists for this student and batch');
    }

    // Handle certificate file upload
    let certificateFile = null;
    if (req.file) {
        certificateFile = await uploadFile(req.file, 'certificates');
    }

    // Generate certificate ID
    const certificateId = await generateUserId('CRT');

    // Create certificate
    const certificate = await Certificate.create({
        certificateId,
        student: student._id,
        batch: batch._id,
        course: courseId,
        certificateType,
        issueDate: new Date(issueDate || Date.now()),
        grade,
        remarks,
        certificateFile,
        status: 'issued',
        issuedBy: req.user._id
    });

    // Populate the certificate with related data
    await certificate.populate([
        { path: 'student', select: 'userId firstName lastName email' },
        { path: 'batch', select: 'batchId batchName' },
        { path: 'course', select: 'courseName courseCode' }
    ]);

    logger.info(`Certificate issued successfully: ${certificateId} for student: ${studentId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, certificate, 'Certificate uploaded successfully')
    );
});

// ==================== DASHBOARD ROUTES ====================

/**
 * Get Dashboard Statistics
 * @route GET /api/admin/dashboard/stats
 * @access Private (Admin only)
 */
const getDashboardStats = asyncHandler(async (req, res) => {
    // Get counts for different entities
    const [
        totalUsers,
        totalStudents,
        totalTrainers,
        totalColleges,
        totalBatches,
        totalCourses,
        totalCertificates,
        activeBatches,
        activeStudents,
        activeTrainers
    ] = await Promise.all([
        User.countDocuments(),
        User.countDocuments({ role: 'student' }),
        User.countDocuments({ role: 'trainer' }),
        User.countDocuments({ role: 'college' }),
        Batch.countDocuments(),
        Course.countDocuments(),
        Certificate.countDocuments(),
        Batch.countDocuments({ status: 'active' }),
        User.countDocuments({ role: 'student', isActive: true }),
        User.countDocuments({ role: 'trainer', isActive: true })
    ]);

    // Get recent registrations (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentRegistrations = await User.countDocuments({
        createdAt: { $gte: thirtyDaysAgo }
    });

    // Get completion rate
    const completedBatches = await Batch.countDocuments({ status: 'completed' });
    const completionRate = totalBatches > 0 ? ((completedBatches / totalBatches) * 100).toFixed(2) : 0;

    const stats = {
        overview: {
            totalUsers,
            totalStudents,
            totalTrainers,
            totalColleges,
            totalBatches,
            totalCourses,
            totalCertificates
        },
        active: {
            activeBatches,
            activeStudents,
            activeTrainers
        },
        metrics: {
            recentRegistrations,
            completionRate: `${completionRate}%`,
            averageStudentsPerBatch: totalBatches > 0 ? Math.round(totalStudents / totalBatches) : 0
        }
    };

    res.status(200).json(
        new ApiResponse(200, stats, 'Dashboard statistics retrieved successfully')
    );
});

/**
 * Get Recent Activities
 * @route GET /api/admin/dashboard/recent-activities
 * @access Private (Admin only)
 */
const getRecentActivities = asyncHandler(async (req, res) => {
    const { limit = 10 } = req.query;

    // Get recent user registrations
    const recentUsers = await User.find({})
        .select('userId firstName lastName role createdAt')
        .sort({ createdAt: -1 })
        .limit(limit / 2);

    // Get recent batch creations
    const recentBatches = await Batch.find({})
        .select('batchId batchName createdAt')
        .populate('course', 'courseName')
        .sort({ createdAt: -1 })
        .limit(limit / 2);

    // Get recent certificates
    const recentCertificates = await Certificate.find({})
        .select('certificateId certificateType issueDate')
        .populate('student', 'userId firstName lastName')
        .sort({ issueDate: -1 })
        .limit(limit / 2);

    // Combine and sort all activities
    const activities = [
        ...recentUsers.map(user => ({
            type: 'user_registration',
            description: `New ${user.role} registered: ${user.firstName} ${user.lastName}`,
            userId: user.userId,
            timestamp: user.createdAt
        })),
        ...recentBatches.map(batch => ({
            type: 'batch_creation',
            description: `New batch created: ${batch.batchName} for ${batch.course.courseName}`,
            batchId: batch.batchId,
            timestamp: batch.createdAt
        })),
        ...recentCertificates.map(cert => ({
            type: 'certificate_issued',
            description: `Certificate issued to ${cert.student.firstName} ${cert.student.lastName}`,
            certificateId: cert.certificateId,
            timestamp: cert.issueDate
        }))
    ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, limit);

    res.status(200).json(
        new ApiResponse(200, activities, 'Recent activities retrieved successfully')
    );
});

module.exports = {
    createStudent,
    createTrainer,
    createCollege,
    createBatch,
    assignBatchToTrainer,
    uploadCourse,
    getAllCourses,
    uploadCertificate,
    getDashboardStats,
    getRecentActivities
};